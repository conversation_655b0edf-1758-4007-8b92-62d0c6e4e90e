import React from 'react';
import { Shield, AlertTriangle, Eye, Lock, TrendingUp, TrendingDown, Activity, Users } from 'lucide-react';

const Dashboard: React.FC = () => {
  const securityMetrics = [
    {
      title: 'Total Threats Blocked',
      value: '1,247',
      change: '+12%',
      trend: 'up',
      icon: Shield,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700'
    },
    {
      title: 'Active Alerts',
      value: '23',
      change: '-8%',
      trend: 'down',
      icon: AlertTriangle,
      color: 'bg-red-500',
      bgColor: 'bg-red-50',
      textColor: 'text-red-700'
    },
    {
      title: 'Security Events',
      value: '8,942',
      change: '+5%',
      trend: 'up',
      icon: Activity,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700'
    },
    {
      title: 'Active Users',
      value: '156',
      change: '+3%',
      trend: 'up',
      icon: Users,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700'
    }
  ];

  const recentAlerts = [
    { id: 1, type: 'High', message: 'Suspicious login attempt detected', time: '2 minutes ago', severity: 'bg-red-100 text-red-800' },
    { id: 2, type: 'Medium', message: 'Unusual network traffic pattern', time: '15 minutes ago', severity: 'bg-yellow-100 text-yellow-800' },
    { id: 3, type: 'Low', message: 'Failed authentication attempt', time: '1 hour ago', severity: 'bg-blue-100 text-blue-800' },
    { id: 4, type: 'High', message: 'Malware signature detected', time: '2 hours ago', severity: 'bg-red-100 text-red-800' },
  ];

  const systemStatus = [
    { name: 'Firewall', status: 'Active', uptime: '99.9%', color: 'bg-green-500' },
    { name: 'Antivirus', status: 'Active', uptime: '99.8%', color: 'bg-green-500' },
    { name: 'IDS/IPS', status: 'Active', uptime: '99.7%', color: 'bg-green-500' },
    { name: 'VPN Gateway', status: 'Warning', uptime: '98.2%', color: 'bg-yellow-500' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Security Dashboard</h1>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Last updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>

      {/* Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {securityMetrics.map((metric, index) => {
          const Icon = metric.icon;
          const TrendIcon = metric.trend === 'up' ? TrendingUp : TrendingDown;
          
          return (
            <div key={index} className={`${metric.bgColor} rounded-xl p-6 border border-gray-200`}>
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-lg ${metric.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className={`flex items-center space-x-1 text-sm ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  <TrendIcon className="h-4 w-4" />
                  <span>{metric.change}</span>
                </div>
              </div>
              <div className="mt-4">
                <h3 className="text-2xl font-bold text-gray-900">{metric.value}</h3>
                <p className={`text-sm ${metric.textColor} mt-1`}>{metric.title}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Alerts */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Recent Alerts</h2>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {recentAlerts.map((alert) => (
              <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${alert.severity}`}>
                  {alert.type}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{alert.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{alert.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">System Status</h2>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">All Systems Operational</span>
            </div>
          </div>
          <div className="space-y-4">
            {systemStatus.map((system, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${system.color}`}></div>
                  <span className="font-medium text-gray-900">{system.name}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{system.status}</p>
                  <p className="text-xs text-gray-500">Uptime: {system.uptime}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Security Chart Placeholder */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Security Events Timeline</h2>
        <div className="h-64 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Security events chart will be displayed here</p>
            <p className="text-sm text-gray-400 mt-1">Integration with charting library needed</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;