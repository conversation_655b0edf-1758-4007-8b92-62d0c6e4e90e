import React, { useState } from 'react';
import { Settings as SettingsIcon, Shield, Bell, Database, Network, Lock, Save, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security');
  const [settings, setSettings] = useState({
    security: {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        passwordExpiry: 90
      },
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      twoFactorAuth: true,
      encryptionLevel: 'AES-256'
    },
    notifications: {
      emailAlerts: true,
      smsAlerts: false,
      pushNotifications: true,
      alertThreshold: 'medium',
      dailyReports: true,
      weeklyReports: true,
      realTimeAlerts: true
    },
    monitoring: {
      logRetention: 365,
      realTimeMonitoring: true,
      automaticBackup: true,
      backupFrequency: 'daily',
      compressionEnabled: true,
      anomalyDetection: true,
      threatIntelligence: true
    },
    network: {
      firewallEnabled: true,
      intrusionDetection: true,
      vpnRequired: false,
      allowedIpRanges: ['***********/24', '10.0.0.0/8'],
      blockedCountries: ['CN', 'RU', 'KP'],
      rateLimiting: true,
      maxRequestsPerMinute: 1000
    }
  });

  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const tabs = [
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'monitoring', name: 'Monitoring', icon: Database },
    { id: 'network', name: 'Network', icon: Network }
  ];

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
    setUnsavedChanges(true);
    setSaveStatus('idle');
  };

  const updateNestedSetting = (category: string, parentKey: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [parentKey]: {
          ...prev[category as keyof typeof prev][parentKey as keyof typeof prev[category as keyof typeof prev]],
          [key]: value
        }
      }
    }));
    setUnsavedChanges(true);
    setSaveStatus('idle');
  };

  const saveSettings = async () => {
    setSaveStatus('saving');
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSaveStatus('saved');
      setUnsavedChanges(false);
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const resetToDefaults = () => {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
      // Reset logic would go here
      setUnsavedChanges(true);
    }
  };

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Password Policy</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Minimum Length
            </label>
            <input
              type="number"
              value={settings.security.passwordPolicy.minLength}
              onChange={(e) => updateNestedSetting('security', 'passwordPolicy', 'minLength', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="6"
              max="32"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password Expiry (days)
            </label>
            <input
              type="number"
              value={settings.security.passwordPolicy.passwordExpiry}
              onChange={(e) => updateNestedSetting('security', 'passwordPolicy', 'passwordExpiry', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="30"
              max="365"
            />
          </div>
        </div>
        
        <div className="mt-4 space-y-3">
          {[
            { key: 'requireUppercase', label: 'Require Uppercase Letters' },
            { key: 'requireLowercase', label: 'Require Lowercase Letters' },
            { key: 'requireNumbers', label: 'Require Numbers' },
            { key: 'requireSpecialChars', label: 'Require Special Characters' }
          ].map(({ key, label }) => (
            <label key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.security.passwordPolicy[key as keyof typeof settings.security.passwordPolicy] as boolean}
                onChange={(e) => updateNestedSetting('security', 'passwordPolicy', key, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">{label}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Authentication</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session Timeout (minutes)
            </label>
            <input
              type="number"
              value={settings.security.sessionTimeout}
              onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="5"
              max="480"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Login Attempts
            </label>
            <input
              type="number"
              value={settings.security.maxLoginAttempts}
              onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="3"
              max="10"
            />
          </div>
        </div>
        
        <div className="mt-4 space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.security.twoFactorAuth}
              onChange={(e) => updateSetting('security', 'twoFactorAuth', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">Enable Two-Factor Authentication</span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Channels</h3>
        <div className="space-y-3">
          {[
            { key: 'emailAlerts', label: 'Email Alerts' },
            { key: 'smsAlerts', label: 'SMS Alerts' },
            { key: 'pushNotifications', label: 'Push Notifications' },
            { key: 'realTimeAlerts', label: 'Real-time Alerts' }
          ].map(({ key, label }) => (
            <label key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.notifications[key as keyof typeof settings.notifications] as boolean}
                onChange={(e) => updateSetting('notifications', key, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">{label}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Threshold</h3>
        <select
          value={settings.notifications.alertThreshold}
          onChange={(e) => updateSetting('notifications', 'alertThreshold', e.target.value)}
          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="low">Low - All events</option>
          <option value="medium">Medium - Important events only</option>
          <option value="high">High - Critical events only</option>
        </select>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Reports</h3>
        <div className="space-y-3">
          {[
            { key: 'dailyReports', label: 'Daily Reports' },
            { key: 'weeklyReports', label: 'Weekly Reports' }
          ].map(({ key, label }) => (
            <label key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.notifications[key as keyof typeof settings.notifications] as boolean}
                onChange={(e) => updateSetting('notifications', key, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">{label}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMonitoringSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Retention</h3>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Log Retention Period (days)
          </label>
          <input
            type="number"
            value={settings.monitoring.logRetention}
            onChange={(e) => updateSetting('monitoring', 'logRetention', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            min="30"
            max="2555"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Monitoring Features</h3>
        <div className="space-y-3">
          {[
            { key: 'realTimeMonitoring', label: 'Real-time Monitoring' },
            { key: 'anomalyDetection', label: 'Anomaly Detection' },
            { key: 'threatIntelligence', label: 'Threat Intelligence' },
            { key: 'compressionEnabled', label: 'Log Compression' }
          ].map(({ key, label }) => (
            <label key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.monitoring[key as keyof typeof settings.monitoring] as boolean}
                onChange={(e) => updateSetting('monitoring', key, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">{label}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Backup Settings</h3>
        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={settings.monitoring.automaticBackup}
              onChange={(e) => updateSetting('monitoring', 'automaticBackup', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">Automatic Backup</span>
          </label>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Backup Frequency
            </label>
            <select
              value={settings.monitoring.backupFrequency}
              onChange={(e) => updateSetting('monitoring', 'backupFrequency', e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNetworkSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Features</h3>
        <div className="space-y-3">
          {[
            { key: 'firewallEnabled', label: 'Firewall Protection' },
            { key: 'intrusionDetection', label: 'Intrusion Detection System' },
            { key: 'vpnRequired', label: 'VPN Required for Access' },
            { key: 'rateLimiting', label: 'Rate Limiting' }
          ].map(({ key, label }) => (
            <label key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.network[key as keyof typeof settings.network] as boolean}
                onChange={(e) => updateSetting('network', key, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">{label}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Control</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Allowed IP Ranges
            </label>
            <textarea
              value={settings.network.allowedIpRanges.join('\n')}
              onChange={(e) => updateSetting('network', 'allowedIpRanges', e.target.value.split('\n').filter(ip => ip.trim()))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="***********/24\n10.0.0.0/8"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Requests Per Minute
            </label>
            <input
              type="number"
              value={settings.network.maxRequestsPerMinute}
              onChange={(e) => updateSetting('network', 'maxRequestsPerMinute', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="100"
              max="10000"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'security': return renderSecuritySettings();
      case 'notifications': return renderNotificationSettings();
      case 'monitoring': return renderMonitoringSettings();
      case 'network': return renderNetworkSettings();
      default: return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <div className="flex items-center space-x-3">
          {unsavedChanges && (
            <span className="text-sm text-amber-600 flex items-center space-x-1">
              <AlertTriangle className="h-4 w-4" />
              <span>Unsaved changes</span>
            </span>
          )}
          
          {saveStatus === 'saved' && (
            <span className="text-sm text-green-600 flex items-center space-x-1">
              <CheckCircle className="h-4 w-4" />
              <span>Settings saved</span>
            </span>
          )}
          
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Reset</span>
          </button>
          
          <button
            onClick={saveSettings}
            disabled={!unsavedChanges || saveStatus === 'saving'}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {saveStatus === 'saving' ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>{saveStatus === 'saving' ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>
        
        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default Settings;