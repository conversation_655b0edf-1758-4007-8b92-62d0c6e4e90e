import React, { useState } from 'react';
import { <PERSON>ert<PERSON><PERSON><PERSON>, <PERSON><PERSON>ircle, Clock, X, <PERSON>, Bell } from 'lucide-react';

const Alerts: React.FC = () => {
  const [alerts, setAlerts] = useState([
    {
      id: 1,
      title: 'Suspicious Login Activity',
      description: 'Multiple failed login attempts detected from IP ************',
      severity: 'High',
      status: 'Active',
      timestamp: '2024-01-15 14:30:25',
      source: 'Authentication System',
      affectedAssets: ['Web Portal', 'User Database'],
      severityColor: 'bg-red-100 text-red-800 border-red-200',
      iconColor: 'text-red-600'
    },
    {
      id: 2,
      title: 'Malware Detection',
      description: 'Trojan.Win32.Generic detected in file system',
      severity: 'Critical',
      status: 'Active',
      timestamp: '2024-01-15 14:25:10',
      source: 'Antivirus Engine',
      affectedAssets: ['Workstation-045', 'File Server'],
      severityColor: 'bg-red-100 text-red-800 border-red-200',
      iconColor: 'text-red-600'
    },
    {
      id: 3,
      title: 'Network Anomaly',
      description: 'Unusual outbound traffic pattern detected',
      severity: 'Medium',
      status: 'Investigating',
      timestamp: '2024-01-15 14:20:45',
      source: 'Network Monitor',
      affectedAssets: ['Network Gateway', 'DMZ'],
      severityColor: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      iconColor: 'text-yellow-600'
    },
    {
      id: 4,
      title: 'Firewall Rule Violation',
      description: 'Blocked connection attempt to restricted port',
      severity: 'Low',
      status: 'Resolved',
      timestamp: '2024-01-15 14:15:30',
      source: 'Firewall',
      affectedAssets: ['External Firewall'],
      severityColor: 'bg-blue-100 text-blue-800 border-blue-200',
      iconColor: 'text-blue-600'
    },
    {
      id: 5,
      title: 'Certificate Expiration Warning',
      description: 'SSL certificate will expire in 7 days',
      severity: 'Medium',
      status: 'Acknowledged',
      timestamp: '2024-01-15 14:10:15',
      source: 'Certificate Monitor',
      affectedAssets: ['Web Server', 'API Gateway'],
      severityColor: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      iconColor: 'text-yellow-600'
    }
  ]);

  const [selectedAlert, setSelectedAlert] = useState<number | null>(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSeverity, setFilterSeverity] = useState('all');

  const handleDismissAlert = (alertId: number) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId 
        ? { ...alert, status: 'Resolved' }
        : alert
    ));
  };

  const handleAcknowledgeAlert = (alertId: number) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId 
        ? { ...alert, status: 'Acknowledged' }
        : alert
    ));
  };

  const filteredAlerts = alerts.filter(alert => {
    const statusMatch = filterStatus === 'all' || alert.status.toLowerCase() === filterStatus.toLowerCase();
    const severityMatch = filterSeverity === 'all' || alert.severity.toLowerCase() === filterSeverity.toLowerCase();
    return statusMatch && severityMatch;
  });

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'investigating': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'acknowledged': return <Eye className="h-4 w-4 text-blue-500" />;
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-red-100 text-red-800';
      case 'investigating': return 'bg-yellow-100 text-yellow-800';
      case 'acknowledged': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const alertCounts = {
    total: alerts.length,
    active: alerts.filter(a => a.status === 'Active').length,
    critical: alerts.filter(a => a.severity === 'Critical').length,
    resolved: alerts.filter(a => a.status === 'Resolved').length
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Security Alerts</h1>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Alerts</p>
              <p className="text-2xl font-bold text-gray-900">{alertCounts.total}</p>
            </div>
            <Bell className="h-8 w-8 text-gray-400" />
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-red-600">{alertCounts.active}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-700">{alertCounts.critical}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-green-600">{alertCounts.resolved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="investigating">Investigating</option>
                <option value="acknowledged">Acknowledged</option>
                <option value="resolved">Resolved</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Severity</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            Showing {filteredAlerts.length} of {alerts.length} alerts
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="space-y-4">
        {filteredAlerts.map((alert) => (
          <div key={alert.id} className={`bg-white rounded-xl border-l-4 ${alert.severityColor} shadow-sm border border-gray-200 p-6`}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <AlertTriangle className={`h-5 w-5 ${alert.iconColor}`} />
                  <h3 className="text-lg font-semibold text-gray-900">{alert.title}</h3>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${alert.severityColor}`}>
                    {alert.severity}
                  </span>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(alert.status)}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(alert.status)}`}>
                      {alert.status}
                    </span>
                  </div>
                </div>
                <p className="text-gray-700 mb-3">{alert.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Source:</span>
                    <span className="ml-2 text-gray-900">{alert.source}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Timestamp:</span>
                    <span className="ml-2 text-gray-900">{alert.timestamp}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Affected Assets:</span>
                    <span className="ml-2 text-gray-900">{alert.affectedAssets.join(', ')}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                {alert.status === 'Active' && (
                  <>
                    <button
                      onClick={() => handleAcknowledgeAlert(alert.id)}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                      Acknowledge
                    </button>
                    <button
                      onClick={() => handleDismissAlert(alert.id)}
                      className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                    >
                      Resolve
                    </button>
                  </>
                )}
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <Eye className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredAlerts.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
          <p className="text-gray-500">No alerts found matching your criteria</p>
        </div>
      )}
    </div>
  );
};

export default Alerts;