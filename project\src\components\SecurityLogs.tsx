import React, { useState } from 'react';
import { Search, Filter, Download, Eye, AlertTriangle, Shield, Activity } from 'lucide-react';

const SecurityLogs: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const logs = [
    {
      id: 1,
      timestamp: '2024-01-15 14:30:25',
      type: 'Authentication',
      severity: 'High',
      source: '*************',
      user: '<EMAIL>',
      event: 'Failed login attempt - Multiple retries detected',
      status: 'Blocked',
      icon: AlertTriangle,
      severityColor: 'bg-red-100 text-red-800'
    },
    {
      id: 2,
      timestamp: '2024-01-15 14:28:15',
      type: 'Network',
      severity: 'Medium',
      source: '*********',
      user: 'system',
      event: 'Unusual outbound traffic detected',
      status: 'Monitoring',
      icon: Activity,
      severityColor: 'bg-yellow-100 text-yellow-800'
    },
    {
      id: 3,
      timestamp: '2024-01-15 14:25:10',
      type: 'Firewall',
      severity: 'Low',
      source: '***********',
      user: 'N/A',
      event: 'Port scan attempt blocked',
      status: 'Blocked',
      icon: Shield,
      severityColor: 'bg-blue-100 text-blue-800'
    },
    {
      id: 4,
      timestamp: '2024-01-15 14:20:45',
      type: 'Malware',
      severity: 'High',
      source: '************',
      user: '<EMAIL>',
      event: 'Malicious file detected and quarantined',
      status: 'Resolved',
      icon: AlertTriangle,
      severityColor: 'bg-red-100 text-red-800'
    },
    {
      id: 5,
      timestamp: '2024-01-15 14:15:30',
      type: 'Authentication',
      severity: 'Low',
      source: '192.168.1.120',
      user: '<EMAIL>',
      event: 'Successful login',
      status: 'Allowed',
      icon: Shield,
      severityColor: 'bg-green-100 text-green-800'
    },
    {
      id: 6,
      timestamp: '2024-01-15 14:10:15',
      type: 'Network',
      severity: 'Medium',
      source: '172.16.0.10',
      user: 'system',
      event: 'Bandwidth threshold exceeded',
      status: 'Monitoring',
      icon: Activity,
      severityColor: 'bg-yellow-100 text-yellow-800'
    }
  ];

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.event.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.source.includes(searchTerm) ||
                         log.user.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || log.type.toLowerCase() === filterType.toLowerCase();
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'blocked': return 'bg-red-100 text-red-800';
      case 'allowed': return 'bg-green-100 text-green-800';
      case 'monitoring': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Security Logs</h1>
        <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          <Download className="h-4 w-4" />
          <span>Export Logs</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="authentication">Authentication</option>
                <option value="network">Network</option>
                <option value="firewall">Firewall</option>
                <option value="malware">Malware</option>
              </select>
            </div>
            
            <div className="text-sm text-gray-500">
              {filteredLogs.length} of {logs.length} logs
            </div>
          </div>
        </div>
      </div>

      {/* Logs Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLogs.map((log) => {
                const Icon = log.icon;
                return (
                  <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.timestamp}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <Icon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-900">{log.type}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${log.severityColor}`}>
                        {log.severity}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.source}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {log.event}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(log.status)}`}>
                        {log.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        {filteredLogs.length === 0 && (
          <div className="text-center py-12">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No logs found matching your criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SecurityLogs;