import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Shield, Activity, AlertTriangle, Users, Settings, BarChart3 } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {
  const location = useLocation();

  const menuItems = [
    { path: '/', icon: BarChart3, label: 'Dashboard', color: 'text-blue-600' },
    { path: '/logs', icon: Activity, label: 'Security Logs', color: 'text-green-600' },
    { path: '/alerts', icon: AlertTriangle, label: 'Alerts', color: 'text-red-600' },
    { path: '/users', icon: Users, label: 'User Management', color: 'text-purple-600' },
    { path: '/settings', icon: Settings, label: 'Settings', color: 'text-gray-600' },
  ];

  return (
    <div className={`fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Shield className="h-8 w-8 text-blue-600" />
          {isOpen && (
            <div>
              <h1 className="text-xl font-bold text-gray-800">SYRA</h1>
              <p className="text-sm text-gray-500">Security Monitor</p>
            </div>
          )}
        </div>
      </div>
      
      <nav className="mt-6">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center px-4 py-3 mx-2 rounded-lg transition-colors duration-200 ${
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
              }`}
            >
              <Icon className={`h-5 w-5 ${isActive ? 'text-blue-700' : item.color}`} />
              {isOpen && (
                <span className="ml-3 font-medium">{item.label}</span>
              )}
            </Link>
          );
        })}
      </nav>
      
      {isOpen && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
            <h3 className="font-semibold text-sm">Security Status</h3>
            <p className="text-xs opacity-90 mt-1">All systems operational</p>
            <div className="flex items-center mt-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="ml-2 text-xs">Online</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;