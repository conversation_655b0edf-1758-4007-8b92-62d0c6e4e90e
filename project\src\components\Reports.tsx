import React, { useState } from 'react';
import { FileText, Download, Calendar, Filter, TrendingUp, TrendingDown, AlertTriangle, Shield, Eye, BarChart3, <PERSON><PERSON>hart, Activity } from 'lucide-react';

const Reports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [selectedReport, setSelectedReport] = useState('security-overview');
  const [isGenerating, setIsGenerating] = useState(false);

  const reportTypes = [
    {
      id: 'security-overview',
      name: 'Security Overview',
      description: 'Comprehensive security status and metrics',
      icon: Shield,
      color: 'blue'
    },
    {
      id: 'threat-analysis',
      name: 'Threat Analysis',
      description: 'Detailed threat detection and response analysis',
      icon: AlertTriangle,
      color: 'red'
    },
    {
      id: 'user-activity',
      name: 'User Activity',
      description: 'User access patterns and behavior analysis',
      icon: Eye,
      color: 'green'
    },
    {
      id: 'compliance',
      name: 'Compliance Report',
      description: 'Regulatory compliance status and audit trail',
      icon: FileText,
      color: 'purple'
    },
    {
      id: 'performance',
      name: 'Performance Metrics',
      description: 'System performance and monitoring statistics',
      icon: Activity,
      color: 'orange'
    }
  ];

  const periods = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const securityMetrics = {
    totalEvents: 15847,
    criticalAlerts: 23,
    resolvedIncidents: 156,
    averageResponseTime: '4.2 min',
    systemUptime: '99.97%',
    threatsBlocked: 1247,
    vulnerabilitiesFound: 8,
    complianceScore: 94
  };

  const recentReports = [
    {
      id: 1,
      name: 'Weekly Security Summary',
      type: 'Security Overview',
      generatedAt: '2024-01-15 09:30:00',
      size: '2.4 MB',
      status: 'completed'
    },
    {
      id: 2,
      name: 'Threat Intelligence Report',
      type: 'Threat Analysis',
      generatedAt: '2024-01-14 16:45:00',
      size: '1.8 MB',
      status: 'completed'
    },
    {
      id: 3,
      name: 'User Access Audit',
      type: 'User Activity',
      generatedAt: '2024-01-14 14:20:00',
      size: '3.1 MB',
      status: 'completed'
    },
    {
      id: 4,
      name: 'Compliance Assessment',
      type: 'Compliance Report',
      generatedAt: '2024-01-13 11:15:00',
      size: '4.2 MB',
      status: 'completed'
    },
    {
      id: 5,
      name: 'Monthly Performance Report',
      type: 'Performance Metrics',
      generatedAt: '2024-01-12 08:00:00',
      size: '1.5 MB',
      status: 'generating'
    }
  ];

  const chartData = {
    securityEvents: [
      { date: '2024-01-09', events: 1245 },
      { date: '2024-01-10', events: 1567 },
      { date: '2024-01-11', events: 1432 },
      { date: '2024-01-12', events: 1789 },
      { date: '2024-01-13', events: 1654 },
      { date: '2024-01-14', events: 1876 },
      { date: '2024-01-15', events: 2284 }
    ],
    threatTypes: [
      { type: 'Malware', count: 456, percentage: 35 },
      { type: 'Phishing', count: 324, percentage: 25 },
      { type: 'Brute Force', count: 267, percentage: 20 },
      { type: 'DDoS', count: 156, percentage: 12 },
      { type: 'Other', count: 97, percentage: 8 }
    ]
  };

  const generateReport = async () => {
    setIsGenerating(true);
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      // In a real app, this would trigger the actual report generation
      alert('Report generated successfully!');
    } catch (error) {
      alert('Error generating report');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadReport = (reportId: number) => {
    // In a real app, this would download the actual report file
    alert(`Downloading report ${reportId}...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'generating': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getReportColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800',
      red: 'bg-red-100 text-red-800',
      green: 'bg-green-100 text-green-800',
      purple: 'bg-purple-100 text-purple-800',
      orange: 'bg-orange-100 text-orange-800'
    };
    return colors[color as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <button
          onClick={generateReport}
          disabled={isGenerating}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Generating...</span>
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              <span>Generate Report</span>
            </>
          )}
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Events</p>
              <p className="text-2xl font-bold text-gray-900">{securityMetrics.totalEvents.toLocaleString()}</p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12% from last week
              </p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
              <p className="text-2xl font-bold text-red-600">{securityMetrics.criticalAlerts}</p>
              <p className="text-xs text-red-600 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +3 from yesterday
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Threats Blocked</p>
              <p className="text-2xl font-bold text-green-600">{securityMetrics.threatsBlocked.toLocaleString()}</p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8% from last week
              </p>
            </div>
            <Shield className="h-8 w-8 text-green-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Compliance Score</p>
              <p className="text-2xl font-bold text-blue-600">{securityMetrics.complianceScore}%</p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +2% from last month
              </p>
            </div>
            <PieChart className="h-8 w-8 text-blue-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Generation */}
        <div className="lg:col-span-2 space-y-6">
          {/* Report Types */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Generate New Report</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Report Type
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {reportTypes.map((report) => {
                    const Icon = report.icon;
                    return (
                      <label key={report.id} className="relative">
                        <input
                          type="radio"
                          name="reportType"
                          value={report.id}
                          checked={selectedReport === report.id}
                          onChange={(e) => setSelectedReport(e.target.value)}
                          className="sr-only"
                        />
                        <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          selectedReport === report.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}>
                          <div className="flex items-start space-x-3">
                            <Icon className={`h-5 w-5 mt-0.5 ${
                              selectedReport === report.id ? 'text-blue-600' : 'text-gray-400'
                            }`} />
                            <div>
                              <p className={`font-medium ${
                                selectedReport === report.id ? 'text-blue-900' : 'text-gray-900'
                              }`}>
                                {report.name}
                              </p>
                              <p className="text-sm text-gray-500 mt-1">
                                {report.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time Period
                  </label>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {periods.map((period) => (
                      <option key={period.value} value={period.value}>
                        {period.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Format
                  </label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="csv">CSV</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Charts */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Security Events Trend</h2>
            <div className="h-64 flex items-end justify-between space-x-2">
              {chartData.securityEvents.map((data, index) => (
                <div key={index} className="flex flex-col items-center space-y-2">
                  <div
                    className="bg-blue-500 rounded-t"
                    style={{
                      height: `${(data.events / Math.max(...chartData.securityEvents.map(d => d.events))) * 200}px`,
                      width: '40px'
                    }}
                  ></div>
                  <span className="text-xs text-gray-500 transform -rotate-45">
                    {new Date(data.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Reports */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Reports</h2>
            <div className="space-y-3">
              {recentReports.map((report) => (
                <div key={report.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{report.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{report.type}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-gray-500">
                          {new Date(report.generatedAt).toLocaleString()}
                        </span>
                        <span className="text-xs text-gray-500">{report.size}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(report.status)}`}>
                        {report.status}
                      </span>
                      {report.status === 'completed' && (
                        <button
                          onClick={() => downloadReport(report.id)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Threat Distribution */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Threat Distribution</h2>
            <div className="space-y-3">
              {chartData.threatTypes.map((threat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span className="text-sm font-medium text-gray-900">{threat.type}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${threat.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-500 w-8">{threat.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;